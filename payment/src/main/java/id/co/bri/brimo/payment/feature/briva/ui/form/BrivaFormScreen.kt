package id.co.bri.brimo.payment.feature.briva.ui.form

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.app.BrivaFormRoute
import id.co.bri.brimo.payment.core.common.launchAndCollectIn
import id.co.bri.brimo.payment.core.common.onError
import id.co.bri.brimo.payment.core.common.onLoading
import id.co.bri.brimo.payment.core.common.onSuccess
import id.co.bri.brimo.payment.core.common.serialize
import id.co.bri.brimo.payment.core.design.component.BottomSheet
import id.co.bri.brimo.payment.core.design.component.DividerHorizontal
import id.co.bri.brimo.payment.core.design.component.ErrorBottomSheet
import id.co.bri.brimo.payment.core.design.component.PrimaryButton
import id.co.bri.brimo.payment.core.design.component.ProgressDialog
import id.co.bri.brimo.payment.core.design.component.SnackbarCustom
import id.co.bri.brimo.payment.core.design.component.SnackbarType
import id.co.bri.brimo.payment.core.design.component.TextFieldCustom
import id.co.bri.brimo.payment.core.design.component.textFieldColors
import id.co.bri.brimo.payment.core.design.theme.Color_0054F3
import id.co.bri.brimo.payment.core.design.theme.Color_7B90A6
import id.co.bri.brimo.payment.core.design.theme.Color_E6EEFF
import id.co.bri.brimo.payment.core.design.theme.Color_E84040
import id.co.bri.brimo.payment.core.design.theme.Color_F5F7FB
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.MessageException
import id.co.bri.brimo.payment.core.network.response.base.FavoriteResponse
import id.co.bri.brimo.payment.core.network.response.briva.BrivaFormResponse
import id.co.bri.brimo.payment.feature.briva.ui.base.BrivaFavoriteSection
import id.co.bri.brimo.payment.feature.briva.ui.base.BrivaHistorySection
import id.co.bri.brimo.payment.feature.briva.ui.base.BrivaSearchDialog
import id.co.bri.brimo.payment.feature.brizzi.ui.favorite.FavoriteBottomSheet
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel

@Composable
internal fun BrivaFormScreen(
    favorite: Pair<String, String>? = null,
    onFavorite: () -> Unit = {},
    navigation: (BrivaFormNavigation) -> Unit = {},
    brivaFormViewModel: BrivaFormViewModel = koinViewModel()
) {
    val brivaForm by brivaFormViewModel.brivaForm.collectAsStateWithLifecycle()
    val favorites by brivaFormViewModel.favorites.collectAsStateWithLifecycle()

    brivaForm
        .onLoading {
            ProgressDialog()
        }
        .onSuccess { data ->
            BrivaFormContent(
                favorite = favorite,
                onFavorite = onFavorite,
                state = BrivaFormState(
                    brivaFormRoute = brivaFormViewModel.brivaFormRoute,
                    brivaForm = data,
                    brivaModel = brivaFormViewModel.brivaModel,
                    pinFavorite = brivaFormViewModel.pinFavorite,
                    deleteFavorite = brivaFormViewModel.deleteFavorite,
                    favorites = favorites
                ),
                event = brivaFormViewModel::handleEvent,
                navigation = navigation
            )
        }
        .onError { error ->
            ErrorBottomSheet(
                error = error,
                onDismiss = {
                    navigation(BrivaFormNavigation.Back)
                },
                onClose = {
                    navigation(BrivaFormNavigation.Back)
                },
                onRetry = {
                    brivaFormViewModel.handleEvent(BrivaFormEvent.RefreshBrivaForm)
                }
            )
        }
}

@Composable
private fun BrivaFormContent(
    favorite: Pair<String, String>? = null,
    onFavorite: () -> Unit = {},
    state: BrivaFormState,
    event: (BrivaFormEvent) -> Unit = {},
    navigation: (BrivaFormNavigation) -> Unit = {}
) {
    val lifecycleOwner = LocalLifecycleOwner.current
    val scope = rememberCoroutineScope()
    val focusManager = LocalFocusManager.current

    // Global
    var numberField by rememberSaveable { mutableStateOf("") }
    val isMin by remember {
        derivedStateOf {
            numberField.isNotEmpty() && numberField.length < 6
        }
    }
    val isMax by remember {
        derivedStateOf {
            numberField.isNotEmpty() && numberField.length > 25
        }
    }
    val enableButton by remember {
        derivedStateOf {
            numberField.isNotEmpty() && numberField.length >= 6 && numberField.length <= 25
        }
    }

    // Loading
    var progressDialog by rememberSaveable { mutableStateOf(false) }
    if (progressDialog) ProgressDialog()

    // Error
    var errorBottomSheet by rememberSaveable { mutableStateOf(false) }
    var error by remember { mutableStateOf(Throwable()) }

    ErrorBottomSheet(
        error = error,
        showBottomSheet = errorBottomSheet,
        onShowBottomSheet = { errorBottomSheet = it },
        onRetry = {
            event(BrivaFormEvent.GetBrivaData(numberField))
        }
    )

    // Snackbar
    val snackbarHostState = remember { SnackbarHostState() }
    var snackbarType by rememberSaveable { mutableStateOf(SnackbarType.INFO) }

    // Search
    var openDialog by rememberSaveable { mutableStateOf(false) }

    // Favorite
    var favoriteBottomSheet by rememberSaveable { mutableStateOf(false) }
    var selectedFavorite: FavoriteResponse? by remember { mutableStateOf(null) }

    if (favorite != null) {
        LaunchedEffect(Unit) {
            scope.launch {
                snackbarType = SnackbarType.SUCCESS
                snackbarHostState.showSnackbar("Nama berhasil diubah.")
            }
            event(BrivaFormEvent.EditFavorite(favorite.first, favorite.second))
            onFavorite()
        }
    }

    BottomSheet(
        showBottomSheet = favoriteBottomSheet,
        onShowBottomSheet = { favoriteBottomSheet = it }
    ) { dismiss ->
        FavoriteBottomSheet(
            isPin = selectedFavorite?.favorite ?: false,
            onPin = {
                dismiss {
                    event(
                        BrivaFormEvent.PinFavorite(
                            savedId = selectedFavorite?.value.orEmpty(),
                            isPin = selectedFavorite?.favorite ?: false
                        )
                    )
                }
            },
            onEditFavorite = {
                dismiss {
                    navigation(
                        BrivaFormNavigation.EditFavorite(
                            number = selectedFavorite?.description.orEmpty(),
                            name = selectedFavorite?.title.orEmpty(),
                            savedId = selectedFavorite?.value.orEmpty(),
                            subtitle = selectedFavorite?.subtitle.orEmpty()
                        )
                    )
                }
            },
            onDeleteFavorite = {
                dismiss {
                    event(
                        BrivaFormEvent.DeleteFavorite(
                            savedId = selectedFavorite?.value.orEmpty()
                        )
                    )
                }
            },
            onClose = {
                dismiss {}
            }
        )
    }

    // Search
    if (openDialog) {
        Dialog(
            onDismissRequest = { openDialog = false },
            properties = DialogProperties(usePlatformDefaultWidth = false)
        ) {
            Surface(modifier = Modifier.fillMaxSize()) {
                BrivaSearchDialog(
                    favorite = state.favorites,
                    history = state.brivaForm.history.orEmpty(),
                    showOption = !state.brivaFormRoute.fastMenu,
                    onOptionFavorite = { item ->
                        selectedFavorite = item
                        favoriteBottomSheet = true
                    },
                    onSelectFavorite = { item ->
                        openDialog = false
                        numberField = item.description.orEmpty()
                        event(BrivaFormEvent.GetBrivaData(numberField))
                    },
                    onSelectHistory = { item ->
                        openDialog = false
                        numberField = item.description.orEmpty()
                        event(BrivaFormEvent.GetBrivaData(numberField))
                    },
                    onBack = {
                        openDialog = false
                    }
                )
            }
        }
    }

    LaunchedEffect(Unit) {
        // Briva Model
        state.brivaModel.launchAndCollectIn(lifecycleOwner) { event ->
            event
                .onLoading {
                    progressDialog = true
                }
                .onSuccess { data ->
                    progressDialog = false
                    val brivaData = data.serialize().orEmpty()
                    if (brivaData.isEmpty()) {
                        errorBottomSheet = true
                    } else {
                        if (data.brivaInquiry?.openPayment == true) {
                            navigation(
                                BrivaFormNavigation.Nominal(
                                    brivaData = brivaData,
                                    fastMenu = state.brivaFormRoute.fastMenu
                                )
                            )
                        } else {
                            navigation(
                                BrivaFormNavigation.Confirmation(
                                    brivaData = brivaData,
                                    fastMenu = state.brivaFormRoute.fastMenu
                                )
                            )
                        }
                    }
                }
                .onError { e ->
                    progressDialog = false
                    if (e is MessageException && e.code !in listOf("05", "61", "93")) {
                        scope.launch {
                            snackbarType = SnackbarType.ERROR
                            snackbarHostState.showSnackbar(e.description)
                        }
                    } else {
                        error = e
                        errorBottomSheet = true
                    }
                }
        }

        // Pin Favorite
        state.pinFavorite.launchAndCollectIn(lifecycleOwner) { event ->
            event
                .onLoading {
                    progressDialog = true
                }
                .onSuccess {
                    progressDialog = false
                    val message = if (selectedFavorite?.favorite == true) {
                        "Pin berhasil dihapus."
                    } else {
                        "Daftar berhasil dipin."
                    }
                    snackbarType = SnackbarType.SUCCESS
                    scope.launch {
                        snackbarHostState.showSnackbar(message)
                    }
                }
                .onError { error ->
                    progressDialog = false
                    val message = if (selectedFavorite?.favorite == true) {
                        "Gagal menghapus, silakan coba lagi, ya."
                    } else {
                        "Daftar gagal dipin. Silakan coba lagi, ya."
                    }
                    snackbarType = SnackbarType.ERROR
                    scope.launch {
                        snackbarHostState.showSnackbar(message)
                    }
                }
        }

        // Delete Favorite
        state.deleteFavorite.launchAndCollectIn(lifecycleOwner) { event ->
            event
                .onLoading {
                    progressDialog = true
                }
                .onSuccess {
                    progressDialog = false
                    val message = "Daftar Favorit berhasil dihapus."
                    snackbarType = SnackbarType.SUCCESS
                    scope.launch {
                        snackbarHostState.showSnackbar(message)
                    }
                }
                .onError { error ->
                    progressDialog = false
                    val message = "Gagal menghapus, silakan coba lagi, ya."
                    snackbarType = SnackbarType.ERROR
                    scope.launch {
                        snackbarHostState.showSnackbar(message)
                    }
                }
        }
    }

    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .imePadding()
            .pointerInput(Unit) {
                detectTapGestures {
                    focusManager.clearFocus()
                }
            },
        bottomBar = {
            if (!state.brivaFormRoute.fastMenu) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Color.White)
                ) {
                    DividerHorizontal()

                    PrimaryButton(
                        label = "Tambah Transaksi Baru",
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        enabled = enableButton
                    ) {
                        focusManager.clearFocus()
                        event(BrivaFormEvent.GetBrivaData(numberField))
                    }
                }
            }
        }
    ) { innerPadding ->
        Box(modifier = Modifier.fillMaxSize()) {
            SnackbarCustom(
                modifier = Modifier
                    .padding(innerPadding)
                    .padding(16.dp),
                hostState = snackbarHostState,
                type = snackbarType
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .paint(
                        painter = painterResource(R.drawable.form_background),
                        sizeToIntrinsics = false,
                        contentScale = ContentScale.Crop
                    )
                    .padding(innerPadding)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(R.drawable.icon_back),
                        contentDescription = null,
                        modifier = Modifier
                            .size(32.dp)
                            .clickable {
                                navigation(BrivaFormNavigation.Back)
                            },
                        contentScale = ContentScale.Fit
                    )

                    Text(
                        text = "BRIVA",
                        modifier = Modifier
                            .weight(1f)
                            .padding(horizontal = 16.dp)
                            .padding(end = 32.dp),
                        color = Color.White,
                        fontWeight = FontWeight.SemiBold,
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.titleLarge
                    )
                }

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            Color.White,
                            RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)
                        )
                        .padding(horizontal = 16.dp)
                ) {
                    Spacer(modifier = Modifier.height(24.dp))

                    if (!state.brivaFormRoute.fastMenu) {
                        var isFocused by rememberSaveable { mutableStateOf(false) }
                        val borderColor = if (isMin || isMax) {
                            Color_E84040
                        } else if (isFocused) {
                            Color_0054F3
                        } else {
                            Color.Transparent
                        }

                        TextFieldCustom(
                            value = numberField,
                            onValueChange = {
                                if (it.length <= 25) {
                                    numberField = it.filter { s -> s.isDigit() }
                                }
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(64.dp)
                                .onFocusChanged { state ->
                                    isFocused = state.isFocused
                                }
                                .border(1.dp, borderColor, RoundedCornerShape(16.dp)),
                            textStyle = MaterialTheme.typography.bodyMedium.copy(
                                fontWeight = FontWeight.SemiBold
                            ),
                            label = {
                                Text(text = "Nomor Virtual Account")
                            },
                            trailingIcon = if (numberField.isNotEmpty()) {
                                {
                                    Image(
                                        painter = painterResource(R.drawable.icon_close),
                                        contentDescription = null,
                                        modifier = Modifier
                                            .size(16.dp)
                                            .clickable {
                                                numberField = ""
                                            },
                                        contentScale = ContentScale.Fit
                                    )
                                }
                            } else {
                                null
                            },
                            keyboardOptions = KeyboardOptions(
                                keyboardType = KeyboardType.Number
                            ),
                            singleLine = true,
                            shape = RoundedCornerShape(16.dp),
                            colors = textFieldColors(),
                            contentPadding = TextFieldDefaults.contentPaddingWithoutLabel(top = 12.dp)
                        )

                        val errorText = when {
                            isMin -> "Masukkan minimal 6 digit nomor Virtual Account"
                            isMax -> "Maksimal 25 digit nomor Virtual Account"
                            else -> ""
                        }

                        if (errorText.isNotEmpty()) {
                            Spacer(modifier = Modifier.height(8.dp))

                            Text(
                                text = errorText,
                                color = Color_E84040,
                                style = MaterialTheme.typography.bodySmall
                            )
                        }

                        Spacer(modifier = Modifier.height(24.dp))

                        DividerHorizontal()

                        Spacer(modifier = Modifier.height(24.dp))
                    }

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color_F5F7FB, RoundedCornerShape(24.dp))
                            .clickable {
                                openDialog = true
                            }
                            .padding(horizontal = 16.dp, vertical = 12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Search,
                            contentDescription = null,
                            modifier = Modifier.size(20.dp)
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        Text(
                            text = "Cari tujuan transaksi",
                            color = Color_7B90A6,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    val tabs = listOf("Favorit", "Riwayat")
                    var page by rememberSaveable { mutableIntStateOf(0) }

                    Row {
                        tabs.forEachIndexed { index, tab ->
                            val selected = page == index
                            val backgroundColor = if (selected) Color_E6EEFF else Color_F5F7FB
                            val textColor = if (selected) Color_0054F3 else Color.Black

                            Text(
                                text = tab,
                                modifier = Modifier
                                    .background(backgroundColor, RoundedCornerShape(100))
                                    .clickable {
                                        page = index
                                    }
                                    .padding(horizontal = 16.dp, vertical = 8.dp),
                                color = textColor,
                                style = MaterialTheme.typography.bodySmall
                            )

                            Spacer(modifier = Modifier.width(12.dp))
                        }
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    val favorite = state.favorites
                    val history = state.brivaForm.history.orEmpty()

                    when (page) {
                        0 -> {
                            BrivaFavoriteSection(
                                data = favorite,
                                showOption = !state.brivaFormRoute.fastMenu,
                                onOption = { item ->
                                    selectedFavorite = item
                                    favoriteBottomSheet = true
                                },
                                onSelect = { item ->
                                    numberField = item.description.orEmpty()
                                    event(BrivaFormEvent.GetBrivaData(numberField))
                                }
                            )
                        }

                        1 -> {
                            BrivaHistorySection(
                                data = history,
                                onSelect = { item ->
                                    numberField = item.description.orEmpty()
                                    event(BrivaFormEvent.GetBrivaData(numberField))
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewBrivaForm() {
    MainTheme {
        BrivaFormContent(
            state = BrivaFormState(
                brivaFormRoute = BrivaFormRoute(
                    fastMenu = false
                ),
                brivaForm = BrivaFormResponse(
                    topBriva = listOf(),
                    history = listOf(),
                    saved = listOf(),
                    referenceNumber = ""
                )
            )
        )
    }
}
