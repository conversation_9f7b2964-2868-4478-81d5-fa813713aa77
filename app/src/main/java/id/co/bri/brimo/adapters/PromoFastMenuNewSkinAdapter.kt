package id.co.bri.brimo.adapters

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.view.animation.AlphaAnimation
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.databinding.ItemPromoFastMenuNewSkinBinding
import id.co.bri.brimo.databinding.ItemPromoFastMenuNewSkinSkeletonBinding
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.DetailPromoResponse

class PromoFastMenuNewSkinAdapter(
    private val context: Context,
    private var promoList: List<DetailPromoResponse>,
    private val clickItem: OnClickItem,
    private var isLoading: Boolean = true,
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val VIEW_TYPE_SKELETON = 0
        private const val VIEW_TYPE_PROMO = 1
        private const val SKELETON_COUNT = 2
    }

    inner class PromoViewHolder(val binding: ItemPromoFastMenuNewSkinBinding) : RecyclerView.ViewHolder(binding.root)
    inner class SkeletonViewHolder(val binding: ItemPromoFastMenuNewSkinSkeletonBinding) : RecyclerView.ViewHolder(binding.root)

    override fun getItemViewType(position: Int): Int {
        return if (isLoading) VIEW_TYPE_SKELETON else VIEW_TYPE_PROMO
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == VIEW_TYPE_SKELETON) {
            val binding = ItemPromoFastMenuNewSkinSkeletonBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            SkeletonViewHolder(binding)
        } else {
            val binding = ItemPromoFastMenuNewSkinBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            PromoViewHolder(binding)
        }
    }


    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is PromoViewHolder) {
            val data = promoList[position]
            GeneralHelper.loadIconTransaction(
                context,
                data.image,
                data.title,
                holder.binding.ivPromo,
                GeneralHelper.getImageId(context, "default_promo")
            )
            holder.binding.llPromo.setOnClickListener {
                clickItem.onClickPromoItem(data)
            }
        } else if (holder is SkeletonViewHolder) {
            val anim = AlphaAnimation(0.3f, 1.0f).apply {
                duration = 500
                repeatMode = AlphaAnimation.REVERSE
                repeatCount = AlphaAnimation.INFINITE
            }
            holder.binding.root.startAnimation(anim)
        }
    }

    override fun getItemCount(): Int {
        return if (isLoading) SKELETON_COUNT else promoList.size
    }

    @SuppressLint("NotifyDataSetChanged")
    fun showSkeleton(isSkeletonVisible: Boolean) {
        isLoading = isSkeletonVisible
        notifyDataSetChanged()
    }

    interface OnClickItem {
        fun onClickPromoItem(promoResponse: DetailPromoResponse)
    }
}
