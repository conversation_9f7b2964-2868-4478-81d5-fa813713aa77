<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_new_skin_activity_container"
    android:fitsSystemWindows="true"
    tools:context=".ui.activities.newskinonboarding.OnboardingOtpNewSkinActivity">

    <include
        android:layout_marginTop="16dp"
        android:id="@+id/toolbar"
        layout="@layout/toolbar_new_skin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="0dp"
        android:layout_marginTop="100dp"
        android:layout_marginEnd="0dp"
        android:background="@drawable/background_cardview_white_newskin"
        android:paddingTop="10dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:weightSum="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_desc_otp"
                    style="@style/BodyText.Large.Regular.BlackNs600"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/space_x2"
                    android:layout_marginTop="@dimen/space_x3"
                    android:textAlignment="center"
                    tools:text="@tools:sample/lorem[10]" />

                <id.co.bri.brimo.ui.customviews.forminput.OtpInputView
                    android:id="@+id/otpInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/size_16dp"
                    android:layout_marginTop="@dimen/space_x5"
                    app:fillWidth="false" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_x7_half"
                    android:gravity="center_horizontal"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_resend_otp"
                        style="@style/TitleText.Small.SemiBold.BlackNs600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/space_half"
                        android:gravity="center"
                        android:text="@string/kirim_ulang_kode" />

                    <TextView
                        android:id="@+id/tv_timer"
                        style="@style/TitleText.Small.SemiBold.BlackNs600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/time00_00"
                        android:textAlignment="center" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tv_counter_otp"
                    style="@style/BodyText.Medium.Regular.BlackNs600"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/space_half"
                    android:layout_marginTop="@dimen/space_x1"
                    android:gravity="center"
                    android:text="Tersisa 1 dari 3 kesempatan"
                    android:visibility="gone" />
            </LinearLayout>

            <id.co.bri.brimo.ui.customviews.keypad.KeypadView
                android:id="@+id/keypad"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:type="number"
                tools:visibility="visible" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>